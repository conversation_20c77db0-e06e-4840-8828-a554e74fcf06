<!DOCTYPE html>
<html lang="en" class="no-js">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover" />
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <link rel="shortcut icon" href="/airsmart.svg" />

    <!-- Untitled Sans Font - matching airsmart.com -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <link rel="stylesheet" type="text/css" href="css/base.css" />
    <link rel="stylesheet" type="text/css" href="css/mobile.css" />
    <title>AirSmart</title>
    <script>
  

      document.documentElement.className = "js";
      var supportsCssVars = function () {
        var e,
          t = document.createElement("style");
        return (
          (t.innerHTML = "root: { --tmp-var: bold; }"),
          document.head.appendChild(t),
          (e = !!(
            window.CSS &&
            window.CSS.supports &&
            window.CSS.supports("font-weight", "var(--tmp-var)")
          )),
          t.parentNode.removeChild(t),
          e
        );
      };
      supportsCssVars() ||
        alert(
          "Please view this demo in a modern browser that supports CSS Variables."
        );
    </script>
  </head>
  <body class="demo-1">
    <main>
       
              
      </div>
      <div id="root"></div>
    </main>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
