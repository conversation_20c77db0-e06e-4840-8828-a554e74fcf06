import { useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  FXAA,
  Vignette
} from '@react-three/postprocessing';
import { useMobile } from '../hooks/useMobile';

/**
 * Simple Post-Processing Pipeline
 * Basic visual enhancement effects:
 * - Bloom effect for realistic lighting
 * - Anti-aliasing (FXAA) for smoother edges
 * - Optional vignette effect
 */
export function PostProcessingPipeline({
  enableBloom = true,
  enableFXAA = true,
  enableVignette = false,
  quality = 'auto'
}) {
  const mobile = useMobile();

  // Simple bloom settings based on device
  const bloomIntensity = useMemo(() => {
    if (mobile.isMobile) return 0.3;
    if (mobile.isTablet) return 0.5;
    return 0.7;
  }, [mobile.isMobile, mobile.isTablet]);

  return (
    <EffectComposer>
      {/* Bloom - Realistic lighting glow */}
      {enableBloom && (
        <Bloom
          intensity={bloomIntensity}
          luminanceThreshold={0.8}
          luminanceSmoothing={0.025}
        />
      )}

      {/* FXAA - Anti-aliasing for smooth edges */}
      {enableFXAA && (
        <FXAA />
      )}

      {/* Optional Vignette effect */}
      {enableVignette && (
        <Vignette
          offset={0.5}
          darkness={0.5}
        />
      )}
    </EffectComposer>
  );
}

/**
 * Preset configurations for different scenarios
 */
export const PostProcessingPresets = {
  // Realistic architectural visualization
  architectural: {
    enableBloom: true,
    enableFXAA: true,
    enableVignette: false,
    quality: 'auto'
  },

  // Cinematic presentation
  cinematic: {
    enableBloom: true,
    enableFXAA: true,
    enableVignette: true,
    quality: 'auto'
  },

  // Performance optimized
  performance: {
    enableBloom: true,
    enableFXAA: false,
    enableVignette: false,
    quality: 'low'
  },

  // Maximum quality
  maxQuality: {
    enableBloom: true,
    enableFXAA: true,
    enableVignette: false,
    quality: 'high'
  }
};

/**
 * Easy-to-use preset component
 */
export function PostProcessingPreset({ preset = 'architectural', ...overrides }) {
  const config = {
    ...PostProcessingPresets[preset],
    ...overrides
  };

  return <PostProcessingPipeline {...config} />;
}
