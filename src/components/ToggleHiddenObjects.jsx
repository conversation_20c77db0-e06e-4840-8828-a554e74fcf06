import { useState, useEffect } from 'react';
import { Html } from '@react-three/drei';
import { useCurrentSheet } from "@theatre/r3f";

const ToggleHiddenObjects = ({ onToggleHidden, isVisible }) => {
  const [isHidden, setIsHidden] = useState(false);
  const sheet = useCurrentSheet();

  // Auto-show objects when exiting range 3.0-4.2 if they were hidden
  useEffect(() => {
    const currentPosition = sheet.sequence.position;

    // Auto-show objects when leaving the range 3.0-4.2 if they are hidden
    if ((currentPosition < 7 || currentPosition > 8.2) && isHidden) {
      setIsHidden(false);
      onToggleHidden(false);
    }
  }, [sheet.sequence.position, isHidden, onToggleHidden]);

  // Check if button should be hidden at start
  const currentPosition = sheet.sequence.position;
  const shouldHideAtStart = currentPosition < 0.1;



  // Always call hooks first (Rules of Hooks)
  const handleToggle = (e) => {
    e.stopPropagation();
    const newHiddenState = !isHidden;
    setIsHidden(newHiddenState);
    onToggleHidden(newHiddenState);
  };

  // Early returns after all hooks
  if (!isVisible || shouldHideAtStart) return null;

  return (
    <group position={[14.75, 4.8, -33.3]} >
      {/* Clickable Text Label - similar to hotspot labels */}
      <Html
        occlude
        position={[0, 0, 0]}
        center
        distanceFactor={8}
        style={{
          pointerEvents: 'auto',
          userSelect: 'none',
        }}
      >
        <div
          onClick={handleToggle}
          style={{
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            fontWeight: 'bold',
            textAlign: 'center',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            whiteSpace: 'nowrap',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
          }}
          onMouseEnter={(e) => {
            e.target.style.transform = 'scale(1.05)';
            e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.target.style.background = 'rgba(0, 0, 0, 0.8)';
            e.target.style.transform = 'scale(1)';
            e.target.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';
          }}
        >
          {isHidden ? 'Show Objects' : 'Hide Objects'}
        </div>
      </Html>
    </group>
  );
};

export default ToggleHiddenObjects;
