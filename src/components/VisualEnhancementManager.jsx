import { useEffect, useMemo } from 'react';
import { useThree } from '@react-three/fiber';
import { useMobile } from '../hooks/useMobile';

/**
 * Visual Enhancement Manager
 * Centralized management for all visual enhancements including:
 * - Performance optimization based on device capabilities
 * - Quality settings management
 * - Feature toggling for mobile/desktop
 * - Memory management and cleanup
 */

/**
 * Performance Monitor Hook
 * Monitors FPS and adjusts quality settings dynamically
 */
export function usePerformanceMonitor() {
  const mobile = useMobile();
  const { gl } = useThree();

  const performanceSettings = useMemo(() => {
    // Base settings for different device types
    const settings = {
      mobile: {
        postProcessing: {
          quality: 'low',
          enableBloom: true,
          enableSSAO: false,
          enableFXAA: true,
          enableToneMapping: true
        },
        grass: {
          grassCount: 1000,
          density: 0.4,
          enableWind: false,
          enableMultipleTypes: false
        },
        shadows: {
          enabled: true,
          mapSize: 512,
          type: 'basic'
        },
        rendering: {
          pixelRatio: Math.min(window.devicePixelRatio, 1.5),
          antialias: false
        }
      },
      tablet: {
        postProcessing: {
          quality: 'medium',
          enableBloom: true,
          enableSSAO: true,
          enableFXAA: true,
          enableToneMapping: true
        },
        grass: {
          grassCount: 2500,
          density: 0.6,
          enableWind: true,
          enableMultipleTypes: false
        },
        shadows: {
          enabled: true,
          mapSize: 1024,
          type: 'soft'
        },
        rendering: {
          pixelRatio: Math.min(window.devicePixelRatio, 2),
          antialias: true
        }
      },
      desktop: {
        postProcessing: {
          quality: 'high',
          enableBloom: true,
          enableSSAO: true,
          enableFXAA: true,
          enableToneMapping: true
        },
        grass: {
          grassCount: 5000,
          density: 0.8,
          enableWind: true,
          enableMultipleTypes: true
        },
        shadows: {
          enabled: true,
          mapSize: 2048,
          type: 'soft'
        },
        rendering: {
          pixelRatio: window.devicePixelRatio,
          antialias: true
        }
      }
    };

    if (mobile.isMobile) return settings.mobile;
    if (mobile.isTablet) return settings.tablet;
    return settings.desktop;
  }, [mobile]);

  return performanceSettings;
}

/**
 * Memory Management Hook
 * Handles cleanup and memory optimization
 */
export function useMemoryManager() {
  const { gl, scene } = useThree();

  useEffect(() => {
    // Cleanup function for memory management
    const cleanup = () => {
      // Dispose of unused textures
      gl.dispose();
      
      // Traverse scene and dispose of geometries and materials
      if (scene) {
        scene.traverse((object) => {
          if (object.geometry) {
            object.geometry.dispose();
          }
          if (object.material) {
            if (Array.isArray(object.material)) {
              object.material.forEach(material => material.dispose());
            } else {
              object.material.dispose();
            }
          }
        });
      }
    };

    // Cleanup on unmount
    return cleanup;
  }, [gl, scene]);

  // Force garbage collection periodically (if available)
  useEffect(() => {
    const interval = setInterval(() => {
      if (window.gc) {
        window.gc();
      }
    }, 30000); // Every 30 seconds

    return () => clearInterval(interval);
  }, []);
}

/**
 * Quality Settings Provider
 * Provides optimized settings for all visual components
 */
export function VisualEnhancementManager({ children }) {
  const performanceSettings = usePerformanceMonitor();
  useMemoryManager();

  return children;
}

/**
 * Adaptive Quality Hook
 * Provides settings that adapt based on performance
 */
export function useAdaptiveQuality() {
  const performanceSettings = usePerformanceMonitor();
  const mobile = useMobile();

  // Post-processing settings
  const postProcessingConfig = useMemo(() => ({
    preset: mobile.isMobile ? 'performance' : 'architectural',
    quality: performanceSettings.postProcessing.quality,
    enableBloom: performanceSettings.postProcessing.enableBloom,
    enableSSAO: performanceSettings.postProcessing.enableSSAO,
    enableFXAA: performanceSettings.postProcessing.enableFXAA,
    enableToneMapping: performanceSettings.postProcessing.enableToneMapping
  }), [performanceSettings, mobile.isMobile]);

  // Grass settings
  const grassConfig = useMemo(() => ({
    size: mobile.isMobile ? 40 : mobile.isTablet ? 60 : 80,
    grassCount: performanceSettings.grass.grassCount,
    density: performanceSettings.grass.density,
    enableWind: performanceSettings.grass.enableWind,
    windStrength: mobile.isMobile ? 0.1 : 0.3,
    windSpeed: mobile.isMobile ? 0.4 : 0.8,
    enableShadows: performanceSettings.shadows.enabled,
    enableMultipleGrassTypes: performanceSettings.grass.enableMultipleTypes,
    enableFlowers: false, // Disabled for performance
    terrainVariation: !mobile.isMobile
  }), [performanceSettings, mobile]);

  // Rendering settings
  const renderingConfig = useMemo(() => ({
    pixelRatio: performanceSettings.rendering.pixelRatio,
    antialias: performanceSettings.rendering.antialias,
    shadowMapSize: performanceSettings.shadows.mapSize,
    shadowType: performanceSettings.shadows.type
  }), [performanceSettings]);

  return {
    postProcessingConfig,
    grassConfig,
    renderingConfig,
    deviceType: mobile.isMobile ? 'mobile' : mobile.isTablet ? 'tablet' : 'desktop'
  };
}

/**
 * Performance Stats Component (for development)
 */
export function PerformanceStats({ enabled = false }) {
  const { gl } = useThree();

  useEffect(() => {
    if (!enabled || !gl) return;

    let frameCount = 0;
    let lastTime = performance.now();

    const measurePerformance = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        console.log(`FPS: ${fps}, Memory: ${gl.info.memory.geometries} geometries, ${gl.info.memory.textures} textures`);
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(measurePerformance);
    };

    measurePerformance();
  }, [enabled, gl]);

  return null;
}

/**
 * Feature Detection Hook
 * Detects browser and device capabilities
 */
export function useFeatureDetection() {
  return useMemo(() => {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
    
    if (!gl) return { webgl: false };

    const capabilities = {
      webgl: true,
      webgl2: !!canvas.getContext('webgl2'),
      maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
      maxRenderBufferSize: gl.getParameter(gl.MAX_RENDERBUFFER_SIZE),
      maxVertexAttribs: gl.getParameter(gl.MAX_VERTEX_ATTRIBS),
      floatTextures: !!gl.getExtension('OES_texture_float'),
      halfFloatTextures: !!gl.getExtension('OES_texture_half_float'),
      depthTextures: !!gl.getExtension('WEBGL_depth_texture'),
      anisotropicFiltering: !!gl.getExtension('EXT_texture_filter_anisotropic'),
      instancedArrays: !!gl.getExtension('ANGLE_instanced_arrays'),
      vertexArrayObjects: !!gl.getExtension('OES_vertex_array_object')
    };

    canvas.remove();
    return capabilities;
  }, []);
}

/**
 * Optimized Settings Provider
 * Provides the best settings based on device capabilities
 */
export function useOptimizedSettings() {
  const features = useFeatureDetection();
  const mobile = useMobile();
  
  return useMemo(() => {
    // Adjust settings based on detected capabilities
    const baseSettings = {
      enableInstancedGrass: features.instancedArrays,
      enableFloatTextures: features.floatTextures && !mobile.isMobile,
      enableAnisotropicFiltering: features.anisotropicFiltering,
      maxTextureSize: Math.min(features.maxTextureSize, mobile.isMobile ? 1024 : 2048),
      enableAdvancedShaders: features.webgl2 && !mobile.isMobile
    };

    return baseSettings;
  }, [features, mobile.isMobile]);
}
