*,
*::after,
*::before {
  box-sizing: border-box;
}

:root {
  font-size: 14px;
  --color-text: #fff;
  --color-bg: #84a4f4;
  --color-link: #fff;
  --color-link-hover: #fff;
}

body {
  margin: 0;
  color: var(--color-text);
  background-color: var(--color-bg);
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 500;
  /* Mobile optimizations */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
}

/* Page Loader */
.js .loading::before,
.js .loading::after {
  content: "";
  position: fixed;
  z-index: 1000;
}

.js .loading::before {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--color-bg);
}

.js .loading::after {
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  margin: -30px 0 0 -30px;
  border-radius: 50%;
  opacity: 0.4;
  background: var(--color-link);
  animation: loaderAnim 0.7s linear infinite alternate forwards;
}

@keyframes loaderAnim {
  to {
    opacity: 1;
    transform: scale3d(0.5, 0.5, 1);
  }
}

a {
  text-decoration: none;
  color: var(--color-link);
  outline: none;
  cursor: pointer;
}

a:hover {
  color: var(--color-link-hover);
  outline: none;
}

/* Better focus styles from https://developer.mozilla.org/en-US/docs/Web/CSS/:focus-visible */
a:focus {
  /* Provide a fallback style for browsers
	 that don't support :focus-visible */
  outline: none;
  background: lightgrey;
}

a:focus:not(:focus-visible) {
  /* Remove the focus indicator on mouse-focus for browsers
	 that do support :focus-visible */
  background: transparent;
}

a:focus-visible {
  /* Draw a very noticeable focus style for
	 keyboard-focus on browsers that do support
	 :focus-visible */
  outline: 2px solid red;
  background: transparent;
}

.unbutton {
  background: none;
  border: 0;
  padding: 0;
  margin: 0;
  font: inherit;
  cursor: pointer;
}

.unbutton:focus {
  outline: none;
}

.frame {
  position: fixed;
  top: 0rem;
  left: 1rem;
  right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: start;
  color: var(--color-title);
  padding: 1rem 2rem;
  z-index: 10;
  background: rgba(255,255,255,0.24);
  border-radius: 0 0 10px 10px;
  box-shadow: 1px 4px 7px rgba(0,0,0,0.05);
}

.frame a:not(.frame__title-back) {
  white-space: nowrap;
  overflow: hidden;
  position: relative;
}

.frame a:not(.frame__title-back)::before {
  content: "";
  height: 1px;
  width: 100%;
  background: currentColor;
  position: absolute;
  top: 90%;
  transition: transform 0.3s;
  transform-origin: 0% 50%;
}

.frame a:not(.frame__title-back):hover::before {
  transform: scaleX(0);
  transform-origin: 100% 50%;
}

.frame__title {
  grid-area: title;
  display: flex;
}

.frame__title-main {
  font-size: 1rem;
  margin: 0;
  font-weight: inherit;
}

.frame__title-back {
  position: relative;
  display: flex;
  align-items: flex-end;
}

.frame__title-back span {
  display: none;
}

.frame__title-back svg {
  fill: currentColor;
}

.frame__prev {
  grid-area: prev;
}

/* Mobile-first responsive design */
@media screen and (max-width: 768px) {
  :root {
    font-size: 12px;
  }

  body {
    font-size: 14px;
  }

  .frame {
    padding: 1rem;
    font-size: 0.9rem;
  }

  .frame__title-main {
    font-size: 0.9rem;
  }
}

@media screen and (min-width: 53em) {
  .frame {
    display: grid;
    grid-template-columns: auto auto 1fr;
    grid-template-rows: auto;
    grid-template-areas: "title prev sponsor";
    justify-content: start;
    grid-gap: 5vw;
  }
}
