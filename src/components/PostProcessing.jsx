import { useEffect, useMemo } from 'react';
import { useThree } from '@react-three/fiber';
import {
  EffectComposer,
  Bloom,
  FXAA,
  ToneMapping,
  Vignette,
  ChromaticAberration,
  DepthOfField,
  N8AO
} from '@react-three/postprocessing';
import { 
  BlendFunction, 
  ToneMappingMode,
  SMAAPreset,
  EdgeDetectionMode,
  PredicationMode
} from 'postprocessing';
import * as THREE from 'three';
import { useMobile } from '../hooks/useMobile';

/**
 * Enhanced Post-Processing Pipeline
 * Provides comprehensive visual enhancement effects including:
 * - Tone mapping for better color representation
 * - Anti-aliasing (FXAA) for smoother edges
 * - Bloom effect for realistic lighting
 * - SSAO for better depth perception
 * - Additional effects for cinematic quality
 */
export function PostProcessingPipeline({ 
  enableBloom = true,
  enableSSAO = true,
  enableFXAA = true,
  enableToneMapping = true,
  enableVignette = false,
  enableChromaticAberration = false,
  enableDepthOfField = false,
  quality = 'auto' // 'low', 'medium', 'high', 'auto'
}) {
  const { gl, scene, camera } = useThree();
  const mobile = useMobile();

  // Auto-detect quality based on device capabilities
  const effectQuality = useMemo(() => {
    if (quality === 'auto') {
      if (mobile.isMobile) return 'low';
      if (mobile.isTablet) return 'medium';
      return 'high';
    }
    return quality;
  }, [quality, mobile.isMobile, mobile.isTablet]);

  // Configure effects based on quality level
  const effectConfig = useMemo(() => {
    const configs = {
      low: {
        bloom: {
          intensity: 0.3,
          luminanceThreshold: 0.9,
          luminanceSmoothing: 0.025,
          mipmapBlur: false,
          kernelSize: 3
        },
        ssao: {
          samples: 16,
          rings: 4,
          distanceThreshold: 0.5,
          distanceFalloff: 0.1,
          rangeThreshold: 0.0015,
          rangeFalloff: 0.01,
          luminanceInfluence: 0.7,
          radius: 0.1825,
          scale: 1.0,
          bias: 0.025
        },
        fxaa: {
          edgeThreshold: 0.166,
          edgeThresholdMin: 0.0833
        }
      },
      medium: {
        bloom: {
          intensity: 0.5,
          luminanceThreshold: 0.8,
          luminanceSmoothing: 0.025,
          mipmapBlur: true,
          kernelSize: 5
        },
        ssao: {
          samples: 32,
          rings: 4,
          distanceThreshold: 0.4,
          distanceFalloff: 0.1,
          rangeThreshold: 0.0015,
          rangeFalloff: 0.01,
          luminanceInfluence: 0.7,
          radius: 0.1825,
          scale: 1.0,
          bias: 0.025
        },
        fxaa: {
          edgeThreshold: 0.125,
          edgeThresholdMin: 0.0625
        }
      },
      high: {
        bloom: {
          intensity: 0.7,
          luminanceThreshold: 0.7,
          luminanceSmoothing: 0.025,
          mipmapBlur: true,
          kernelSize: 7
        },
        ssao: {
          samples: 64,
          rings: 7,
          distanceThreshold: 0.3,
          distanceFalloff: 0.1,
          rangeThreshold: 0.0015,
          rangeFalloff: 0.01,
          luminanceInfluence: 0.7,
          radius: 0.1825,
          scale: 1.0,
          bias: 0.025
        },
        fxaa: {
          edgeThreshold: 0.1,
          edgeThresholdMin: 0.05
        }
      }
    };

    return configs[effectQuality];
  }, [effectQuality]);

  // Configure renderer for post-processing
  useEffect(() => {
    if (!gl) return;

    // Ensure proper setup for post-processing
    gl.autoClear = false;
    gl.shadowMap.enabled = true;
    gl.shadowMap.type = THREE.PCFSoftShadowMap;
    
    // Configure for HDR workflow
    gl.toneMapping = THREE.NoToneMapping; // Let post-processing handle tone mapping
    gl.outputEncoding = THREE.sRGBEncoding;
    
  }, [gl]);

  return (
    <EffectComposer
      multisampling={effectQuality === 'high' ? 8 : effectQuality === 'medium' ? 4 : 0}
      frameBufferType={effectQuality === 'high' ? THREE.HalfFloatType : THREE.UnsignedByteType}
    >
      {/* Tone Mapping - First in pipeline for proper HDR handling */}
      {enableToneMapping && (
        <ToneMapping
          mode={ToneMappingMode.ACES_FILMIC}
          resolution={256}
          whitePoint={4.0}
          middleGrey={0.6}
          minLuminance={0.01}
          averageLuminance={1.0}
          adaptationRate={1.0}
        />
      )}

      {/* N8AO - Better Ambient Occlusion for depth perception */}
      {enableSSAO && (
        <N8AO
          aoRadius={0.5}
          intensity={effectQuality === 'high' ? 1.0 : effectQuality === 'medium' ? 0.8 : 0.6}
          aoSamples={effectQuality === 'high' ? 16 : effectQuality === 'medium' ? 8 : 4}
          denoiseSamples={effectQuality === 'high' ? 8 : effectQuality === 'medium' ? 4 : 2}
          denoiseRadius={12}
          distanceFalloff={1.0}
          screenSpaceRadius={false}
          halfRes={mobile.isMobile}
          depthAwareUpsampling={!mobile.isMobile}
        />
      )}

      {/* Bloom - Realistic lighting glow */}
      {enableBloom && (
        <Bloom
          blendFunction={BlendFunction.ADD}
          intensity={effectConfig.bloom.intensity}
          width={effectQuality === 'high' ? 1024 : effectQuality === 'medium' ? 512 : 256}
          height={effectQuality === 'high' ? 1024 : effectQuality === 'medium' ? 512 : 256}
          kernelSize={effectConfig.bloom.kernelSize}
          luminanceThreshold={effectConfig.bloom.luminanceThreshold}
          luminanceSmoothing={effectConfig.bloom.luminanceSmoothing}
          mipmapBlur={effectConfig.bloom.mipmapBlur}
        />
      )}

      {/* FXAA - Anti-aliasing for smooth edges */}
      {enableFXAA && (
        <FXAA
          edgeThreshold={effectConfig.fxaa.edgeThreshold}
          edgeThresholdMin={effectConfig.fxaa.edgeThresholdMin}
        />
      )}

      {/* Optional Vignette effect */}
      {enableVignette && (
        <Vignette
          offset={0.5}
          darkness={0.5}
          eskil={false}
          blendFunction={BlendFunction.NORMAL}
        />
      )}

      {/* Optional Chromatic Aberration */}
      {enableChromaticAberration && (
        <ChromaticAberration
          blendFunction={BlendFunction.NORMAL}
          offset={[0.0005, 0.0012]}
        />
      )}

      {/* Optional Depth of Field */}
      {enableDepthOfField && (
        <DepthOfField
          focusDistance={0.0}
          focalLength={0.02}
          bokehScale={2.0}
          height={480}
        />
      )}
    </EffectComposer>
  );
}

/**
 * Preset configurations for different scenarios
 */
export const PostProcessingPresets = {
  // Realistic architectural visualization
  architectural: {
    enableBloom: true,
    enableSSAO: true,
    enableFXAA: true,
    enableToneMapping: true,
    enableVignette: false,
    enableChromaticAberration: false,
    enableDepthOfField: false,
    quality: 'auto'
  },

  // Cinematic presentation
  cinematic: {
    enableBloom: true,
    enableSSAO: true,
    enableFXAA: true,
    enableToneMapping: true,
    enableVignette: true,
    enableChromaticAberration: true,
    enableDepthOfField: false,
    quality: 'auto'
  },

  // Performance optimized
  performance: {
    enableBloom: true,
    enableSSAO: false,
    enableFXAA: true,
    enableToneMapping: true,
    enableVignette: false,
    enableChromaticAberration: false,
    enableDepthOfField: false,
    quality: 'low'
  },

  // Maximum quality
  maxQuality: {
    enableBloom: true,
    enableSSAO: true,
    enableFXAA: true,
    enableToneMapping: true,
    enableVignette: true,
    enableChromaticAberration: false,
    enableDepthOfField: false,
    quality: 'high'
  }
};

/**
 * Easy-to-use preset component
 */
export function PostProcessingPreset({ preset = 'architectural', ...overrides }) {
  const config = {
    ...PostProcessingPresets[preset],
    ...overrides
  };

  return <PostProcessingPipeline {...config} />;
}
