/* Mobile-specific optimizations */

/* Prevent zoom on input focus */
input, select, textarea {
  font-size: 16px !important;
}

/* Improve touch targets */
button, a, [role="button"] {
  min-height: 44px;
  min-width: 44px;
}

/* Prevent text selection on touch */
.no-select {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Smooth scrolling for mobile */
html {
  -webkit-overflow-scrolling: touch;
}

/* Hide scrollbars on mobile */
::-webkit-scrollbar {
  display: none;
}

/* Improve tap highlighting */
* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

/* Mobile-specific animations */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Landscape orientation adjustments */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .mobile-landscape-adjust {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  
  .mobile-landscape-text {
    font-size: 0.9rem !important;
    line-height: 1.3 !important;
  }
}

/* Portrait orientation adjustments */
@media screen and (orientation: portrait) and (max-width: 768px) {
  .mobile-portrait-adjust {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi-optimize {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Touch-friendly spacing */
@media (max-width: 768px) {
  .touch-spacing {
    margin: 0.5rem 0 !important;
  }
  
  .touch-padding {
    padding: 1rem !important;
  }
  
  .touch-button {
    padding: 12px 24px !important;
    margin: 8px 0 !important;
    border-radius: 8px !important;
  }
}

/* Prevent overscroll bounce on iOS */
body {
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
}

/* Fix viewport issues on mobile browsers */
@supports (-webkit-touch-callout: none) {
  .ios-fix {
    height: -webkit-fill-available;
  }
}

/* Android-specific fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .android-fix {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }
}

/* Improve performance on mobile */
.gpu-accelerated {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: transform;
}

/* Mobile-friendly focus states */
@media (max-width: 768px) {
  button:focus,
  a:focus,
  input:focus,
  select:focus,
  textarea:focus {
    outline: 2px solid #007AFF;
    outline-offset: 2px;
  }
}

/* Responsive text scaling */
@media (max-width: 480px) {
  .responsive-text-sm {
    font-size: 0.875rem !important;
  }
  
  .responsive-text-base {
    font-size: 1rem !important;
  }
  
  .responsive-text-lg {
    font-size: 1.125rem !important;
  }
}

/* Safe area insets for notched devices */
@supports (padding: max(0px)) {
  .safe-area-inset {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
    padding-top: max(1rem, env(safe-area-inset-top));
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

/* Improve Canvas performance on mobile */
canvas {
  touch-action: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* Mobile loading optimizations */
@media (max-width: 768px) {
  .mobile-loading {
    animation-duration: 0.8s !important;
  }
  
  .mobile-spinner {
    width: 40px !important;
    height: 40px !important;
  }
}
