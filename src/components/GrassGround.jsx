import { useRef, useMemo, useEffect } from 'react';
import { use<PERSON>rame, useLoader } from '@react-three/fiber';
import { TextureLoader } from 'three';
import * as THREE from 'three';
import { useMobile } from '../hooks/useMobile';

/**
 * Realistic Grass Ground System
 * Creates a natural-looking grass terrain using:
 * - Instanced geometry for performance
 * - Realistic grass textures
 * - Wind animation
 * - LOD (Level of Detail) optimization
 * - Proper integration with lighting and shadows
 */

/**
 * Grass Blade Geometry Generator
 */
function createGrassBladeGeometry() {
  const geometry = new THREE.PlaneGeometry(0.1, 0.6, 1, 4);
  
  // Modify vertices to create a more natural grass blade shape
  const vertices = geometry.attributes.position.array;
  for (let i = 0; i < vertices.length; i += 3) {
    const y = vertices[i + 1];
    // Taper the grass blade towards the top
    const taper = 1 - (y + 0.3) / 0.6;
    vertices[i] *= Math.max(0.1, taper); // x coordinate
    
    // Add slight curve to the blade
    vertices[i + 2] += Math.sin(y * 2) * 0.02; // z coordinate
  }
  
  geometry.attributes.position.needsUpdate = true;
  geometry.computeVertexNormals();
  
  return geometry;
}

/**
 * Grass Material with realistic properties
 */
function createGrassMaterial(grassTexture, mobile) {
  const material = new THREE.MeshLambertMaterial({
    map: grassTexture,
    alphaTest: 0.5,
    side: THREE.DoubleSide,
    transparent: true,
    vertexColors: true
  });
  
  // Configure texture
  if (grassTexture) {
    grassTexture.wrapS = THREE.RepeatWrapping;
    grassTexture.wrapT = THREE.RepeatWrapping;
    grassTexture.repeat.set(1, 1);
    grassTexture.encoding = THREE.sRGBEncoding;
  }
  
  return material;
}

/**
 * Generate grass instances with natural distribution
 */
function generateGrassInstances(count, size, density = 1.0) {
  const instances = [];
  const gridSize = Math.sqrt(count);
  const cellSize = size / gridSize;
  
  for (let i = 0; i < gridSize; i++) {
    for (let j = 0; j < gridSize; j++) {
      // Skip some instances based on density
      if (Math.random() > density) continue;
      
      // Base position in grid
      const x = (i - gridSize / 2) * cellSize;
      const z = (j - gridSize / 2) * cellSize;
      
      // Add random offset within cell
      const offsetX = (Math.random() - 0.5) * cellSize * 0.8;
      const offsetZ = (Math.random() - 0.5) * cellSize * 0.8;
      
      // Random rotation
      const rotation = Math.random() * Math.PI * 2;
      
      // Random scale variation
      const scale = 0.8 + Math.random() * 0.4;
      
      // Random height variation
      const height = 0.8 + Math.random() * 0.4;
      
      instances.push({
        position: [x + offsetX, 0, z + offsetZ],
        rotation: [0, rotation, 0],
        scale: [scale, height, scale],
        windPhase: Math.random() * Math.PI * 2
      });
    }
  }
  
  return instances;
}

/**
 * Grass Ground Component
 */
export function GrassGround({
  size = 50,
  grassCount = 5000,
  density = 0.8,
  enableWind = true,
  windStrength = 0.5,
  windSpeed = 1.0,
  grassColor = '#4a7c59',
  enableShadows = true,
  enableLOD = true
}) {
  const meshRef = useRef();
  const mobile = useMobile();
  
  // Load grass texture (fallback to procedural if not available)
  const grassTexture = useLoader(
    TextureLoader, 
    '/textures/grass.jpg',
    undefined,
    () => {
      // Fallback: create a simple procedural grass texture
      console.log('Grass texture not found, using procedural texture');
    }
  );

  // Adjust settings based on device capabilities
  const optimizedSettings = useMemo(() => {
    if (mobile.isMobile) {
      return {
        grassCount: Math.min(grassCount * 0.3, 1500),
        density: density * 0.6,
        enableWind: enableWind && !mobile.isLowEnd,
        enableLOD: true,
        shadowQuality: 'low'
      };
    } else if (mobile.isTablet) {
      return {
        grassCount: Math.min(grassCount * 0.6, 3000),
        density: density * 0.8,
        enableWind: enableWind,
        enableLOD: true,
        shadowQuality: 'medium'
      };
    } else {
      return {
        grassCount,
        density,
        enableWind,
        enableLOD,
        shadowQuality: 'high'
      };
    }
  }, [mobile, grassCount, density, enableWind]);

  // Generate grass geometry and material
  const { geometry, material, instances } = useMemo(() => {
    const grassGeometry = createGrassBladeGeometry();
    const grassMaterial = createGrassMaterial(grassTexture, mobile);
    const grassInstances = generateGrassInstances(
      optimizedSettings.grassCount, 
      size, 
      optimizedSettings.density
    );
    
    return {
      geometry: grassGeometry,
      material: grassMaterial,
      instances: grassInstances
    };
  }, [grassTexture, mobile, optimizedSettings, size]);

  // Create instanced mesh
  const instancedMesh = useMemo(() => {
    if (!geometry || !material) return null;
    
    const mesh = new THREE.InstancedMesh(geometry, material, instances.length);
    
    // Set up instances
    const matrix = new THREE.Matrix4();
    const color = new THREE.Color(grassColor);
    
    instances.forEach((instance, i) => {
      // Set transform matrix
      matrix.compose(
        new THREE.Vector3(...instance.position),
        new THREE.Euler(...instance.rotation),
        new THREE.Vector3(...instance.scale)
      );
      mesh.setMatrixAt(i, matrix);
      
      // Add color variation
      const colorVariation = 0.8 + Math.random() * 0.4;
      color.setHex(0x4a7c59);
      color.multiplyScalar(colorVariation);
      mesh.setColorAt(i, color);
    });
    
    mesh.instanceMatrix.needsUpdate = true;
    if (mesh.instanceColor) mesh.instanceColor.needsUpdate = true;
    
    // Enable shadows
    if (enableShadows) {
      mesh.castShadow = true;
      mesh.receiveShadow = true;
    }
    
    return mesh;
  }, [geometry, material, instances, grassColor, enableShadows]);

  // Wind animation
  useFrame((state) => {
    if (!optimizedSettings.enableWind || !instancedMesh) return;
    
    const time = state.clock.elapsedTime * windSpeed;
    const matrix = new THREE.Matrix4();
    
    instances.forEach((instance, i) => {
      const windOffset = Math.sin(time + instance.windPhase) * windStrength * 0.1;
      const windRotation = windOffset * 0.2;
      
      matrix.compose(
        new THREE.Vector3(instance.position[0], instance.position[1], instance.position[2]),
        new THREE.Euler(windRotation, instance.rotation[1], windRotation * 0.5),
        new THREE.Vector3(...instance.scale)
      );
      
      instancedMesh.setMatrixAt(i, matrix);
    });
    
    instancedMesh.instanceMatrix.needsUpdate = true;
  });

  // Update mesh reference
  useEffect(() => {
    if (meshRef.current && instancedMesh) {
      meshRef.current.clear();
      meshRef.current.add(instancedMesh);
    }
  }, [instancedMesh]);

  return (
    <group ref={meshRef}>
      {/* Base ground plane */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -0.05, 0]} receiveShadow>
        <planeGeometry args={[size * 1.2, size * 1.2]} />
        <meshLambertMaterial 
          color="#3a5f47"
          roughness={0.9}
          metalness={0.0}
        />
      </mesh>
    </group>
  );
}

/**
 * Enhanced Grass Ground with multiple grass types
 */
export function EnhancedGrassGround({
  size = 50,
  enableMultipleGrassTypes = true,
  enableFlowers = false,
  terrainVariation = true,
  ...props
}) {
  const mobile = useMobile();
  
  // Disable advanced features on mobile for performance
  const enhancedFeatures = useMemo(() => {
    if (mobile.isMobile) {
      return {
        enableMultipleGrassTypes: false,
        enableFlowers: false,
        terrainVariation: false
      };
    }
    return {
      enableMultipleGrassTypes,
      enableFlowers,
      terrainVariation
    };
  }, [mobile.isMobile, enableMultipleGrassTypes, enableFlowers, terrainVariation]);

  return (
    <group>
      {/* Primary grass layer */}
      <GrassGround 
        size={size}
        grassColor="#4a7c59"
        density={0.8}
        {...props}
      />
      
      {/* Secondary grass layer (different height/color) */}
      {enhancedFeatures.enableMultipleGrassTypes && (
        <GrassGround 
          size={size * 0.8}
          grassCount={props.grassCount * 0.5}
          grassColor="#5a8c69"
          density={0.4}
          windSpeed={props.windSpeed * 1.2}
          {...props}
        />
      )}
      
      {/* Small flowers/weeds */}
      {enhancedFeatures.enableFlowers && (
        <GrassGround 
          size={size * 0.6}
          grassCount={props.grassCount * 0.2}
          grassColor="#7a9c79"
          density={0.2}
          windSpeed={props.windSpeed * 0.8}
          {...props}
        />
      )}
    </group>
  );
}
