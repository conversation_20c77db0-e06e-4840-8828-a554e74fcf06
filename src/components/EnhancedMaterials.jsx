import { useEffect, useMemo } from 'react';
import { useThree } from '@react-three/fiber';
import * as THREE from 'three';
import { useMobile } from '../hooks/useMobile';

/**
 * Enhanced Material System for Realistic Reflections
 * Provides advanced PBR material enhancement with:
 * - Improved environment mapping
 * - Material-specific reflection properties
 * - Realistic metalness and roughness values
 * - Optimized reflection probes
 */

/**
 * Material type detection and classification
 */
const MaterialClassifier = {
  // Detect material type based on name patterns
  classifyMaterial: (materialName, meshName) => {
    const name = (materialName || meshName || '').toLowerCase();
    
    // Metal materials
    if (name.includes('metal') || name.includes('steel') || name.includes('aluminum') || 
        name.includes('chrome') || name.includes('brass') || name.includes('copper')) {
      return 'metal';
    }
    
    // Glass materials
    if (name.includes('glass') || name.includes('window') || name.includes('transparent')) {
      return 'glass';
    }
    
    // Plastic materials
    if (name.includes('plastic') || name.includes('polymer') || name.includes('acrylic')) {
      return 'plastic';
    }
    
    // Wood materials
    if (name.includes('wood') || name.includes('timber') || name.includes('oak') || 
        name.includes('pine') || name.includes('mahogany')) {
      return 'wood';
    }
    
    // Ceramic/tile materials
    if (name.includes('ceramic') || name.includes('tile') || name.includes('porcelain')) {
      return 'ceramic';
    }
    
    // Fabric materials
    if (name.includes('fabric') || name.includes('cloth') || name.includes('textile') || 
        name.includes('carpet') || name.includes('upholstery')) {
      return 'fabric';
    }
    
    // Concrete/stone materials
    if (name.includes('concrete') || name.includes('stone') || name.includes('marble') || 
        name.includes('granite') || name.includes('brick')) {
      return 'stone';
    }
    
    // Default to generic material
    return 'generic';
  }
};

/**
 * Material property presets for different material types
 */
const MaterialPresets = {
  metal: {
    metalness: 0.9,
    roughness: 0.1,
    envMapIntensity: 2.0,
    reflectivity: 0.9,
    clearcoat: 0.0,
    clearcoatRoughness: 0.0
  },
  glass: {
    metalness: 0.0,
    roughness: 0.0,
    envMapIntensity: 1.0,
    reflectivity: 0.9,
    transmission: 0.9,
    thickness: 0.5,
    clearcoat: 1.0,
    clearcoatRoughness: 0.0
  },
  plastic: {
    metalness: 0.0,
    roughness: 0.3,
    envMapIntensity: 0.8,
    reflectivity: 0.5,
    clearcoat: 0.5,
    clearcoatRoughness: 0.2
  },
  wood: {
    metalness: 0.0,
    roughness: 0.8,
    envMapIntensity: 0.3,
    reflectivity: 0.2,
    clearcoat: 0.0,
    clearcoatRoughness: 0.0
  },
  ceramic: {
    metalness: 0.0,
    roughness: 0.2,
    envMapIntensity: 0.6,
    reflectivity: 0.4,
    clearcoat: 0.3,
    clearcoatRoughness: 0.1
  },
  fabric: {
    metalness: 0.0,
    roughness: 0.9,
    envMapIntensity: 0.1,
    reflectivity: 0.1,
    clearcoat: 0.0,
    clearcoatRoughness: 0.0
  },
  stone: {
    metalness: 0.0,
    roughness: 0.7,
    envMapIntensity: 0.2,
    reflectivity: 0.3,
    clearcoat: 0.0,
    clearcoatRoughness: 0.0
  },
  generic: {
    metalness: 0.1,
    roughness: 0.5,
    envMapIntensity: 0.5,
    reflectivity: 0.4,
    clearcoat: 0.0,
    clearcoatRoughness: 0.0
  }
};

/**
 * Enhanced Material Enhancer Hook
 */
export function useEnhancedMaterialSystem() {
  const { scene } = useThree();
  const mobile = useMobile();

  const enhanceMaterialWithReflections = useMemo(() => {
    return (material, meshName = '') => {
      if (!material) return material;

      // Classify material type
      const materialType = MaterialClassifier.classifyMaterial(material.name, meshName);
      const preset = MaterialPresets[materialType];

      // Convert to MeshPhysicalMaterial for advanced features if needed
      if (material.isMeshStandardMaterial && !material.isMeshPhysicalMaterial) {
        // Create new MeshPhysicalMaterial with enhanced properties
        const enhancedMaterial = new THREE.MeshPhysicalMaterial();
        
        // Copy basic properties
        enhancedMaterial.copy(material);
        
        // Apply enhanced properties
        enhancedMaterial.metalness = preset.metalness;
        enhancedMaterial.roughness = preset.roughness;
        enhancedMaterial.envMapIntensity = mobile.isMobile ? preset.envMapIntensity * 0.7 : preset.envMapIntensity;
        enhancedMaterial.reflectivity = preset.reflectivity;
        
        // Advanced physical properties
        if (materialType === 'glass') {
          enhancedMaterial.transmission = preset.transmission;
          enhancedMaterial.thickness = preset.thickness;
          enhancedMaterial.transparent = true;
          enhancedMaterial.opacity = 0.9;
        }
        
        // Clearcoat for glossy surfaces
        if (preset.clearcoat > 0) {
          enhancedMaterial.clearcoat = preset.clearcoat;
          enhancedMaterial.clearcoatRoughness = preset.clearcoatRoughness;
        }
        
        // Ensure proper color space
        if (enhancedMaterial.map) {
          enhancedMaterial.map.encoding = THREE.sRGBEncoding;
        }
        
        // Force update
        enhancedMaterial.needsUpdate = true;
        
        return enhancedMaterial;
      }
      
      // Enhance existing material
      material.metalness = preset.metalness;
      material.roughness = preset.roughness;
      material.envMapIntensity = mobile.isMobile ? preset.envMapIntensity * 0.7 : preset.envMapIntensity;
      
      if (material.isMeshPhysicalMaterial) {
        material.reflectivity = preset.reflectivity;
        
        if (materialType === 'glass') {
          material.transmission = preset.transmission;
          material.thickness = preset.thickness;
          material.transparent = true;
          material.opacity = 0.9;
        }
        
        if (preset.clearcoat > 0) {
          material.clearcoat = preset.clearcoat;
          material.clearcoatRoughness = preset.clearcoatRoughness;
        }
      }
      
      // Ensure proper color space
      if (material.map) {
        material.map.encoding = THREE.sRGBEncoding;
      }
      
      material.needsUpdate = true;
      return material;
    };
  }, [mobile.isMobile]);

  const enhanceSceneMaterials = useMemo(() => {
    return (sceneObject) => {
      if (!sceneObject) return;
      
      sceneObject.traverse((child) => {
        if (child.isMesh && child.material) {
          // Handle both single materials and material arrays
          if (Array.isArray(child.material)) {
            child.material = child.material.map(mat => 
              enhanceMaterialWithReflections(mat, child.name)
            );
          } else {
            child.material = enhanceMaterialWithReflections(child.material, child.name);
          }
        }
      });
    };
  }, [enhanceMaterialWithReflections]);

  return {
    enhanceMaterialWithReflections,
    enhanceSceneMaterials,
    MaterialClassifier,
    MaterialPresets
  };
}

/**
 * Enhanced Material System Component
 * Automatically enhances all materials in the scene
 */
export function EnhancedMaterialSystem({ 
  autoEnhance = true,
  materialOverrides = {},
  enableAdvancedFeatures = true 
}) {
  const { scene } = useThree();
  const { enhanceSceneMaterials } = useEnhancedMaterialSystem();

  useEffect(() => {
    if (!scene || !autoEnhance) return;

    // Apply material enhancements to the entire scene
    enhanceSceneMaterials(scene);

    // Apply any custom material overrides
    if (Object.keys(materialOverrides).length > 0) {
      scene.traverse((child) => {
        if (child.isMesh && child.material && child.name in materialOverrides) {
          const overrides = materialOverrides[child.name];
          Object.assign(child.material, overrides);
          child.material.needsUpdate = true;
        }
      });
    }

  }, [scene, autoEnhance, materialOverrides, enhanceSceneMaterials]);

  return null;
}
