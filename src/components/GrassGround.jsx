import { useRef, useMemo, useEffect } from 'react';
import * as THREE from 'three';
import { useMobile } from '../hooks/useMobile';

/**
 * Realistic Grass Ground System
 * Creates a natural-looking grass terrain using:
 * - Instanced geometry for performance
 * - Realistic grass textures
 * - Wind animation
 * - LOD (Level of Detail) optimization
 * - Proper integration with lighting and shadows
 */

/**
 * Grass Blade Geometry Generator
 */
function createGrassBladeGeometry() {
  const geometry = new THREE.PlaneGeometry(0.1, 0.6, 1, 4);
  
  // Modify vertices to create a more natural grass blade shape
  const vertices = geometry.attributes.position.array;
  for (let i = 0; i < vertices.length; i += 3) {
    const y = vertices[i + 1];
    // Taper the grass blade towards the top
    const taper = 1 - (y + 0.3) / 0.6;
    vertices[i] *= Math.max(0.1, taper); // x coordinate
    
    // Add slight curve to the blade
    vertices[i + 2] += Math.sin(y * 2) * 0.02; // z coordinate
  }
  
  geometry.attributes.position.needsUpdate = true;
  geometry.computeVertexNormals();
  
  return geometry;
}

/**
 * Grass Material with realistic properties
 */
function createGrassMaterial(grassTexture, mobile) {
  const material = new THREE.MeshLambertMaterial({
    map: grassTexture,
    alphaTest: 0.5,
    side: THREE.DoubleSide,
    transparent: true,
    vertexColors: true
  });
  
  // Configure texture
  if (grassTexture) {
    grassTexture.wrapS = THREE.RepeatWrapping;
    grassTexture.wrapT = THREE.RepeatWrapping;
    grassTexture.repeat.set(1, 1);
    grassTexture.encoding = THREE.sRGBEncoding;
  }
  
  return material;
}

/**
 * Generate grass instances with natural distribution
 */
function generateGrassInstances(count, size, density = 1.0) {
  const instances = [];
  const gridSize = Math.sqrt(count);
  const cellSize = size / gridSize;
  
  for (let i = 0; i < gridSize; i++) {
    for (let j = 0; j < gridSize; j++) {
      // Skip some instances based on density
      if (Math.random() > density) continue;
      
      // Base position in grid
      const x = (i - gridSize / 2) * cellSize;
      const z = (j - gridSize / 2) * cellSize;
      
      // Add random offset within cell
      const offsetX = (Math.random() - 0.5) * cellSize * 0.8;
      const offsetZ = (Math.random() - 0.5) * cellSize * 0.8;
      
      // Random rotation
      const rotation = Math.random() * Math.PI * 2;
      
      // Random scale variation
      const scale = 0.8 + Math.random() * 0.4;
      
      // Random height variation
      const height = 0.8 + Math.random() * 0.4;
      
      instances.push({
        position: [x + offsetX, 0, z + offsetZ],
        rotation: [0, rotation, 0],
        scale: [scale, height, scale]
      });
    }
  }
  
  return instances;
}

/**
 * Grass Ground Component
 */
export function GrassGround({
  size = 50,
  grassCount = 5000,
  density = 0.8,
  grassColor = '#4a7c59',
  enableShadows = true,
  enableLOD = true
}) {
  const meshRef = useRef();
  const mobile = useMobile();
  
  // Create procedural grass texture since external texture may not exist
  const grassTexture = useMemo(() => {
    // Create a simple canvas-based grass texture
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d');

    // Create grass-like pattern
    ctx.fillStyle = '#4a7c59';
    ctx.fillRect(0, 0, 64, 64);

    // Add some variation
    for (let i = 0; i < 100; i++) {
      const x = Math.random() * 64;
      const y = Math.random() * 64;
      const brightness = 0.8 + Math.random() * 0.4;
      ctx.fillStyle = `rgba(74, 124, 89, ${brightness})`;
      ctx.fillRect(x, y, 2, 8);
    }

    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.repeat.set(4, 4);
    texture.encoding = THREE.sRGBEncoding;

    return texture;
  }, []);

  // Adjust settings based on device capabilities
  const optimizedSettings = useMemo(() => {
    if (mobile.isMobile) {
      return {
        grassCount: Math.min(grassCount * 0.3, 1500),
        density: density * 0.6,
        enableLOD: true,
        shadowQuality: 'low'
      };
    } else if (mobile.isTablet) {
      return {
        grassCount: Math.min(grassCount * 0.6, 3000),
        density: density * 0.8,
        enableLOD: true,
        shadowQuality: 'medium'
      };
    } else {
      return {
        grassCount,
        density,
        enableLOD,
        shadowQuality: 'high'
      };
    }
  }, [mobile, grassCount, density]);

  // Generate grass geometry and material
  const { geometry, material, instances } = useMemo(() => {
    const grassGeometry = createGrassBladeGeometry();
    const grassMaterial = createGrassMaterial(grassTexture, mobile);
    const grassInstances = generateGrassInstances(
      optimizedSettings.grassCount, 
      size, 
      optimizedSettings.density
    );
    
    return {
      geometry: grassGeometry,
      material: grassMaterial,
      instances: grassInstances
    };
  }, [grassTexture, mobile, optimizedSettings, size]);

  // Create instanced mesh
  const instancedMesh = useMemo(() => {
    if (!geometry || !material) return null;
    
    const mesh = new THREE.InstancedMesh(geometry, material, instances.length);
    
    // Set up instances
    const matrix = new THREE.Matrix4();
    const color = new THREE.Color(grassColor);
    
    instances.forEach((instance, i) => {
      // Set transform matrix
      matrix.compose(
        new THREE.Vector3(...instance.position),
        new THREE.Euler(...instance.rotation),
        new THREE.Vector3(...instance.scale)
      );
      mesh.setMatrixAt(i, matrix);
      
      // Add color variation
      const colorVariation = 0.8 + Math.random() * 0.4;
      color.setHex(0x4a7c59);
      color.multiplyScalar(colorVariation);
      mesh.setColorAt(i, color);
    });
    
    mesh.instanceMatrix.needsUpdate = true;
    if (mesh.instanceColor) mesh.instanceColor.needsUpdate = true;
    
    // Enable shadows
    if (enableShadows) {
      mesh.castShadow = true;
      mesh.receiveShadow = true;
    }
    
    return mesh;
  }, [geometry, material, instances, grassColor, enableShadows]);

  // No wind animation - static grass for better performance

  // Update mesh reference
  useEffect(() => {
    if (meshRef.current && instancedMesh) {
      meshRef.current.clear();
      meshRef.current.add(instancedMesh);
    }
  }, [instancedMesh]);

  return (
    <group ref={meshRef}>
      {/* Base ground plane */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -0.05, 0]} receiveShadow>
        <planeGeometry args={[size * 1.2, size * 1.2]} />
        <meshLambertMaterial 
          color="#3a5f47"
          roughness={0.9}
          metalness={0.0}
        />
      </mesh>
    </group>
  );
}

/**
 * Enhanced Grass Ground with multiple grass types
 */
export function EnhancedGrassGround({
  size = 50,
  enableMultipleGrassTypes = true,
  enableFlowers = false,
  terrainVariation = true,
  ...props
}) {
  const mobile = useMobile();
  
  // Disable advanced features on mobile for performance
  const enhancedFeatures = useMemo(() => {
    if (mobile.isMobile) {
      return {
        enableMultipleGrassTypes: false,
        enableFlowers: false,
        terrainVariation: false
      };
    }
    return {
      enableMultipleGrassTypes,
      enableFlowers,
      terrainVariation
    };
  }, [mobile.isMobile, enableMultipleGrassTypes, enableFlowers, terrainVariation]);

  return (
    <group>
      {/* Primary grass layer */}
      <GrassGround 
        size={size}
        grassColor="#4a7c59"
        density={0.8}
        {...props}
      />
      
      {/* Secondary grass layer (different height/color) */}
      {enhancedFeatures.enableMultipleGrassTypes && (
        <GrassGround
          size={size * 0.8}
          grassCount={props.grassCount * 0.5}
          grassColor="#5a8c69"
          density={0.4}
          {...props}
        />
      )}

      {/* Small flowers/weeds */}
      {enhancedFeatures.enableFlowers && (
        <GrassGround
          size={size * 0.6}
          grassCount={props.grassCount * 0.2}
          grassColor="#7a9c79"
          density={0.2}
          {...props}
        />
      )}
    </group>
  );
}
