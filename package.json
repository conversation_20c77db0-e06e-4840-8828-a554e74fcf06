{"name": "camera-animation", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "setup": "yarn dev --host 0.0.0.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.842.0", "@aws-sdk/s3-request-presigner": "^3.842.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@react-three/drei": "^9.56.20", "@react-three/fiber": "^8.11.0", "@react-three/postprocessing": "^3.0.4", "@theatre/core": "^0.6.0", "@theatre/r3f": "^0.6.0", "@theatre/studio": "^0.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.30.1", "three": "^0.149.0"}, "devDependencies": {"@vitejs/plugin-react": "^3.1.0", "typescript": "^4.9.3", "vite": "^4.1.0"}}